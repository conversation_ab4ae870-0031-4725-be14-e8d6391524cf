"""
Test script to verify file moving functionality works correctly.
"""
import sys
import os
import shutil

# Add the current directory to the path so we can import modules
sys.path.append(os.path.dirname(__file__))

from campaign_reporting.data.file_service import get_merchant_output_path, ensure_merchant_directory

def test_file_moving():
    """Test moving files to merchant directories."""
    print("🧪 Testing File Moving Functionality")
    print("=" * 50)
    
    # Test merchant and campaign IDs
    test_merchant = "6294bb10ce2ebe00011b543e"
    test_campaign = "344532343"
    
    # Create test files in output_pdf directory (simulating the original behavior)
    os.makedirs('output_pdf', exist_ok=True)
    
    test_html_filename = f'campaign_metrics_Lista_Gerencial_{test_merchant}_{test_campaign}.html'
    test_pdf_filename = f'campaign_metrics_Lista_Gerencial_{test_merchant}_{test_campaign}.pdf'
    
    test_html_path = os.path.join('output_pdf', test_html_filename)
    test_pdf_path = os.path.join('output_pdf', test_pdf_filename)
    
    # Create test files
    with open(test_html_path, 'w') as f:
        f.write('<html><body>Test HTML content</body></html>')
    
    with open(test_pdf_path, 'w') as f:
        f.write('Test PDF content')
    
    print(f"✅ Created test files:")
    print(f"   - {test_html_path}")
    print(f"   - {test_pdf_path}")
    
    # Test moving files to merchant directory
    merchant_html_path = get_merchant_output_path(test_merchant, test_html_filename)
    merchant_pdf_path = get_merchant_output_path(test_merchant, test_pdf_filename)
    
    print(f"\n📁 Target paths:")
    print(f"   - {merchant_html_path}")
    print(f"   - {merchant_pdf_path}")
    
    # Move files
    try:
        if os.path.exists(test_html_path):
            shutil.move(test_html_path, merchant_html_path)
            print(f"✅ Moved HTML file successfully")
        else:
            print(f"❌ HTML file not found: {test_html_path}")
        
        if os.path.exists(test_pdf_path):
            shutil.move(test_pdf_path, merchant_pdf_path)
            print(f"✅ Moved PDF file successfully")
        else:
            print(f"❌ PDF file not found: {test_pdf_path}")
            
    except Exception as e:
        print(f"❌ Error moving files: {e}")
    
    # Verify files were moved
    print(f"\n🔍 Verification:")
    if os.path.exists(merchant_html_path):
        print(f"✅ HTML file exists in merchant directory")
    else:
        print(f"❌ HTML file not found in merchant directory")
    
    if os.path.exists(merchant_pdf_path):
        print(f"✅ PDF file exists in merchant directory")
    else:
        print(f"❌ PDF file not found in merchant directory")
    
    # Check if original files still exist
    if os.path.exists(test_html_path):
        print(f"⚠️  Original HTML file still exists (should have been moved)")
    
    if os.path.exists(test_pdf_path):
        print(f"⚠️  Original PDF file still exists (should have been moved)")
    
    print(f"\n🎉 File moving test completed!")

if __name__ == "__main__":
    test_file_moving()
