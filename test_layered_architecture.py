"""
Test script to validate the layered architecture implementation.
"""
import asyncio
import logging
import sys
import os
from datetime import datetime

# Add the current directory to the path so we can import modules
sys.path.append(os.path.dirname(__file__))

# Test imports to verify the architecture
try:
    from campaign_reporting.config.settings import DATE_FORMAT, create_retry_options
    from campaign_reporting.business.report_generator import generate_whatsapp_performance_report
    from campaign_reporting.data.file_service import ensure_output_directory
    print("✅ All imports successful - Architecture is properly structured")
except ImportError as e:
    print(f"❌ Import error: {e}")
    exit(1)

def test_config_layer():
    """Test configuration layer."""
    print("\n🧪 Testing Configuration Layer...")

    # Test date format
    current_date = datetime.now().strftime(DATE_FORMAT)
    print(f"   Date format: {current_date}")

    # Test retry options
    retry_options = create_retry_options()
    print(f"   Retry options created: {type(retry_options).__name__}")

    print("✅ Configuration layer working")

def test_data_layer():
    """Test data layer."""
    print("\n🧪 Testing Data Layer...")

    # Test file service
    try:
        ensure_output_directory()
        print("   Output directory ensured")

        # Test merchant directory creation
        from campaign_reporting.data.file_service import ensure_merchant_directory, get_merchant_output_path
        test_merchant = "test_merchant_123"
        merchant_dir = ensure_merchant_directory(test_merchant)
        print(f"   Merchant directory created: {merchant_dir}")

        # Test merchant file path
        test_file_path = get_merchant_output_path(test_merchant, "test_file.txt")
        print(f"   Merchant file path: {test_file_path}")

    except Exception as e:
        print(f"   Error: {e}")
        return False

    print("✅ Data layer working")
    return True

def test_business_layer():
    """Test business layer."""
    print("\n🧪 Testing Business Layer...")

    # Create sample campaign data
    sample_campaign_data = {
        'name': 'Test Campaign',
        'last_updated': '2025-01-01T10:00:00',
        'date_created': '2025-01-01T09:00:00',
        'acos_target': 15.0,
        'budget': 100.0,
        'metrics': {
            'cost': 50.0,
            'total_amount': 200.0,
            'organic_units_amount': 100.0,
            'acos': 25.0,
            'advertising_items_quantity': 5,
            'organic_items_quantity': 3,
            'cvr': 2.5,
            'clicks': 100
        }
    }

    try:
        # Test report generation
        report = generate_whatsapp_performance_report(sample_campaign_data, None, 'pt')
        print(f"   Report generated: {len(report)} characters")
        print(f"   Report preview: {report[:100]}...")
    except Exception as e:
        print(f"   Error generating report: {e}")
        return False

    print("✅ Business layer working")
    return True

def test_architecture_separation():
    """Test that layers are properly separated."""
    print("\n🧪 Testing Architecture Separation...")

    # Check that each layer only imports what it should
    import campaign_reporting.config.settings as config
    import campaign_reporting.data.file_service as data
    import campaign_reporting.business.report_generator as business

    print("   Config layer: ✅")
    print("   Data layer: ✅")
    print("   Business layer: ✅")

    print("✅ Architecture separation verified")

def main():
    """Run all tests."""
    print("🚀 Testing Layered Architecture Implementation")
    print("=" * 50)

    try:
        test_config_layer()
        test_data_layer()
        test_business_layer()
        test_architecture_separation()

        print("\n" + "=" * 50)
        print("🎉 All tests passed! Layered architecture is working correctly.")
        print("\nYou can now run the new system with:")
        print("   python campaign_reporting_main.py")

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return False

    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
