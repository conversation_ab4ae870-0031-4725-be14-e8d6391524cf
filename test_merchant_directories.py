"""
Test script to verify merchant directory creation works correctly.
"""
import sys
import os

# Add the current directory to the path so we can import modules
sys.path.append(os.path.dirname(__file__))

from campaign_reporting.data.file_service import (
    ensure_merchant_directory,
    get_merchant_output_path,
    write_whatsapp_report,
    append_campaign_to_csv
)

def test_merchant_directory_creation():
    """Test creating merchant directories and files."""
    print("🧪 Testing Merchant Directory Creation")
    print("=" * 50)
    
    # Test merchant ID from the logs
    test_merchant = "6294bb10ce2ebe00011b543e"
    
    print(f"Testing with merchant ID: {test_merchant}")
    
    # Test directory creation
    merchant_dir = ensure_merchant_directory(test_merchant)
    print(f"✅ Merchant directory created: {merchant_dir}")
    
    # Test file path generation
    test_file = "test_campaign_123.txt"
    file_path = get_merchant_output_path(test_merchant, test_file)
    print(f"✅ File path generated: {file_path}")
    
    # Test writing a WhatsApp report
    test_content = "This is a test WhatsApp report for merchant directory testing."
    write_whatsapp_report(test_content, test_merchant, "test_campaign_123")
    print(f"✅ WhatsApp report written")
    
    # Test CSV writing
    test_campaign_data = {
        'id': 'test_campaign_123',
        'name': 'Test Campaign',
        'metrics': {
            'cost': 100.0,
            'total_amount': 500.0,
            'acos': 20.0
        }
    }
    append_campaign_to_csv(test_campaign_data, test_merchant)
    print(f"✅ CSV data appended")
    
    # Verify files were created
    print("\n📁 Checking created files:")
    
    # Check if directory exists
    if os.path.exists(merchant_dir):
        print(f"✅ Directory exists: {merchant_dir}")
        
        # List files in directory
        files = os.listdir(merchant_dir)
        print(f"📄 Files in directory: {files}")
        
        for file in files:
            file_path = os.path.join(merchant_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"   - {file} ({file_size} bytes)")
    else:
        print(f"❌ Directory does not exist: {merchant_dir}")
    
    print("\n🎉 Test completed!")

if __name__ == "__main__":
    test_merchant_directory_creation()
